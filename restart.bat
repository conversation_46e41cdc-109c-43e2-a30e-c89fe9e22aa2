@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo 班级管理系统启动脚本
echo ========================================
echo:

REM 检查Node.js是否安装
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查npm是否可用
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到npm，请检查Node.js安装
    pause
    exit /b 1
)

echo 正在检查依赖...

REM 安装后端依赖
if not exist "server\node_modules" (
    echo 安装后端依赖...
    pushd server
    call npm install
    if !errorlevel! neq 0 (
        echo 错误: 后端依赖安装失败
        popd
        pause
        exit /b 1
    )
    popd
    echo 后端依赖安装完成
) else (
    echo 后端依赖已存在
)

REM 安装前端依赖
if not exist "client\node_modules" (
    echo 安装前端依赖...
    pushd client
    call npm install
    if !errorlevel! neq 0 (
        echo 错误: 前端依赖安装失败
        popd
        pause
        exit /b 1
    )
    popd
    echo 前端依赖安装完成
) else (
    echo 前端依赖已存在
)

echo:
echo 正在初始化数据库...
pushd server
call npm run init-db
if !errorlevel! neq 0 (
    echo 警告: 数据库初始化失败，可能已经初始化过
)
popd

echo:
echo 启动服务器...
echo 后端服务器: http://localhost:3001
echo 前端开发服务器: http://localhost:5173
echo:
echo 默认管理员账户:
echo 用户名: admin
echo 密码: admin123
echo:
echo 按 Ctrl+C 停止服务器
echo:

REM 启动后端服务器
echo 启动后端服务器...
start "后端服务器" cmd /k "cd /d %~dp0server && npm run dev"

REM 等待后端启动
timeout /t 5 /nobreak >nul

REM 启动前端服务器
echo 启动前端服务器...
start "前端服务器" cmd /k "cd /d %~dp0client && npm run dev"

echo 服务器启动中，请稍候...
timeout /t 8 /nobreak >nul

echo 打开浏览器...
start http://localhost:5173

echo:
echo 服务器已启动！
echo 如需停止服务器，请关闭对应的命令行窗口
echo:
pause

# 班级管理系统 PowerShell 启动脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "班级管理系统启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Node.js是否安装
try {
    $nodeVersion = node --version
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到Node.js，请先安装Node.js" -ForegroundColor Red
    Write-Host "下载地址: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 获取脚本所在目录
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

Write-Host "正在检查依赖..." -ForegroundColor Yellow

# 检查并安装后端依赖
if (!(Test-Path "server\node_modules")) {
    Write-Host "安装后端依赖..." -ForegroundColor Yellow
    Set-Location "server"
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 后端依赖安装失败" -ForegroundColor Red
        Read-Host "按回车键退出"
        exit 1
    }
    Set-Location ".."
    Write-Host "后端依赖安装完成" -ForegroundColor Green
} else {
    Write-Host "后端依赖已存在" -ForegroundColor Green
}

# 检查并安装前端依赖
if (!(Test-Path "client\node_modules")) {
    Write-Host "安装前端依赖..." -ForegroundColor Yellow
    Set-Location "client"
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 前端依赖安装失败" -ForegroundColor Red
        Read-Host "按回车键退出"
        exit 1
    }
    Set-Location ".."
    Write-Host "前端依赖安装完成" -ForegroundColor Green
} else {
    Write-Host "前端依赖已存在" -ForegroundColor Green
}

Write-Host ""
Write-Host "正在初始化数据库..." -ForegroundColor Yellow
Set-Location "server"
npm run init-db
Set-Location ".."

Write-Host ""
Write-Host "启动服务器..." -ForegroundColor Yellow
Write-Host "后端服务器: http://localhost:3001" -ForegroundColor Cyan
Write-Host "前端开发服务器: http://localhost:5173" -ForegroundColor Cyan
Write-Host ""
Write-Host "默认管理员账户:" -ForegroundColor Green
Write-Host "用户名: admin" -ForegroundColor Green
Write-Host "密码: admin123" -ForegroundColor Green
Write-Host ""

# 启动后端服务器
Write-Host "启动后端服务器..." -ForegroundColor Yellow
$serverPath = Join-Path $scriptPath "server"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Set-Location '$serverPath'; npm run dev"

# 等待后端启动
Start-Sleep -Seconds 5

# 启动前端服务器
Write-Host "启动前端服务器..." -ForegroundColor Yellow
$clientPath = Join-Path $scriptPath "client"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Set-Location '$clientPath'; npm run dev"

Write-Host "服务器启动中，请稍候..." -ForegroundColor Yellow
Start-Sleep -Seconds 8

# 打开浏览器
Write-Host "打开浏览器..." -ForegroundColor Yellow
Start-Process "http://localhost:5173"

Write-Host ""
Write-Host "服务器已启动！" -ForegroundColor Green
Write-Host "如需停止服务器，请关闭对应的PowerShell窗口" -ForegroundColor Yellow
Write-Host ""
Read-Host "按回车键退出此窗口"

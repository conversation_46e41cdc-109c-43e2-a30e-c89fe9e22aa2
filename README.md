# 班级管理系统

一个现代化的H5班级管理Web应用，帮助信息任课老师管理班级纪律，促进学生努力学习，创造良好的学习环境。

## 🎯 项目特色

- **匿名反馈系统** - 学生可以匿名反馈班级情况和违纪情况
- **分级权限管理** - 班长、纪律委员、卫生委员、监督委员各司其职
- **响应式设计** - 完美适配手机、平板、电脑等各种设备
- **实时数据统计** - 直观的数据面板，掌握班级动态
- **容器化部署** - 支持Docker一键部署，方便运维

## 🏗️ 技术架构

### 前端技术栈
- **React 19** - 现代化前端框架
- **Ant Design** - 企业级UI组件库
- **Vite** - 快速构建工具
- **Axios** - HTTP客户端
- **React Router** - 路由管理

### 后端技术栈
- **Node.js** - 服务器运行环境
- **Express** - Web应用框架
- **SQLite** - 轻量级数据库
- **JWT** - 身份认证
- **bcryptjs** - 密码加密

### 部署技术
- **Docker** - 容器化部署
- **Nginx** - 反向代理（可选）

## 📋 功能模块

### 1. 匿名反馈系统
- 学生可匿名提交班级问题反馈
- 支持多种反馈类型：纪律问题、卫生问题、学习环境、其他建议
- 可设置问题严重程度：轻微、一般、严重
- 完全匿名，保护学生隐私

### 2. 管理员后台
- **班长** - 拥有所有管理权限，可查看和处理所有反馈和违纪记录
- **纪律委员** - 主要负责纪律相关问题的管理
- **卫生委员** - 主要负责卫生相关问题的管理  
- **监督委员** - 监督其他委员的工作，确保公正性

### 3. 违纪记录管理
- 管理员可记录学生违纪情况
- 支持多种违纪类型和严重程度分级
- 违纪记录状态跟踪：记录、已处理、已警告
- 学生违纪统计分析

### 4. 数据统计分析
- 实时数据面板显示班级概况
- 反馈处理进度统计
- 违纪情况趋势分析
- 各类问题分布统计

## 🚀 快速开始

### 方式一：一键启动（Windows推荐）

**最简单的启动方式，适合Windows用户：**

1. **安装依赖**
   ```bash
   # 双击运行安装脚本
   install.bat
   ```

2. **启动系统**
   ```bash
   # 双击运行启动脚本
   run.bat
   ```

3. **访问应用**
   - 前端页面：http://localhost:5173
   - 匿名反馈：http://localhost:5173/feedback
   - 管理后台：http://localhost:5173/admin

### 方式二：Docker部署

1. **克隆项目**
```bash
git clone <repository-url>
cd classmanage
```

2. **使用Docker Compose启动**
```bash
# 启动应用
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止应用
docker-compose down
```

3. **访问应用**
- 前端页面：http://localhost:3001
- 匿名反馈：http://localhost:3001/feedback
- 管理后台：http://localhost:3001/admin

### 方式三：手动启动

1. **环境要求**
- Node.js 18+
- npm 或 yarn

2. **安装依赖**
```bash
# 安装所有依赖
npm run install:all

# 或分别安装
cd server && npm install
cd ../client && npm install
```

3. **初始化数据库**
```bash
cd server
npm run init-db
```

4. **启动开发服务器**
```bash
# 同时启动前后端（推荐）
npm run dev

# 或分别启动
npm run server:dev  # 后端服务 (端口3001)
npm run client:dev  # 前端服务 (端口5173)
```

## 🔐 默认账户

系统预设了一个管理员账户：

- **用户名**: `admin`
- **密码**: `admin123`
- **角色**: 班长（拥有所有权限）

> ⚠️ **安全提醒**: 生产环境中请及时修改默认密码！

## 📱 使用指南

### 学生使用
1. 访问 `/feedback` 页面
2. 选择反馈类型和严重程度
3. 详细描述问题情况
4. 提交匿名反馈

### 管理员使用
1. 访问 `/login` 页面登录
2. 进入管理后台查看数据概览
3. 在"反馈管理"中处理学生反馈
4. 在"违纪管理"中记录和处理违纪情况
5. 查看统计分析了解班级动态

## 🐳 Docker部署详解

### 基础部署
```bash
# 构建并启动
docker-compose up -d

# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs class-management
```

### 生产环境部署（带Nginx）
```bash
# 启动包含Nginx的完整服务
docker-compose --profile production up -d

# 这将启动：
# - 班级管理应用 (端口3001)
# - Nginx反向代理 (端口80/443)
```

### 数据持久化
- 数据库文件自动持久化到Docker卷 `class_management_data`
- 数据不会因容器重启而丢失

### 环境变量配置
可以通过修改 `server/.env` 文件或在 `docker-compose.yml` 中设置环境变量：

```yaml
environment:
  - NODE_ENV=production
  - JWT_SECRET=your_secret_key_here
  - PORT=3001
```

## 🔧 配置说明

### 服务器配置 (server/.env)
```env
PORT=3001                    # 服务器端口
NODE_ENV=development         # 运行环境
JWT_SECRET=your_secret_key   # JWT密钥
FRONTEND_URL=http://localhost:5173  # 前端URL（CORS配置）
```

### 前端配置 (client/.env)
```env
VITE_API_URL=http://localhost:3001/api  # API服务器地址
```

## 📊 数据库结构

系统使用SQLite数据库，包含以下主要表：

- **users** - 管理员用户表
- **feedbacks** - 匿名反馈表
- **violations** - 违纪记录表
- **actions** - 处理记录表
- **statistics** - 统计数据表

## 🛠️ 开发指南

### 项目结构
```
classmanage/
├── client/                 # 前端代码
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   ├── hooks/         # 自定义Hook
│   │   └── utils/         # 工具函数
│   └── package.json
├── server/                 # 后端代码
│   ├── routes/            # 路由处理
│   ├── models/            # 数据模型
│   ├── middleware/        # 中间件
│   ├── scripts/           # 脚本文件
│   └── package.json
├── docker-compose.yml      # Docker编排文件
├── Dockerfile             # Docker镜像构建文件
└── README.md
```

### API接口文档

#### 认证接口
- `POST /api/auth/login` - 管理员登录
- `GET /api/auth/verify` - 验证token
- `POST /api/auth/logout` - 登出

#### 反馈接口
- `POST /api/feedback/submit` - 提交匿名反馈
- `GET /api/feedback/list` - 获取反馈列表（需认证）
- `PUT /api/feedback/:id/status` - 更新反馈状态（需认证）

#### 违纪记录接口
- `POST /api/violation/create` - 创建违纪记录（需认证）
- `GET /api/violation/list` - 获取违纪记录列表（需认证）
- `PUT /api/violation/:id/status` - 更新违纪记录状态（需认证）

## 🔒 安全特性

- JWT身份认证
- 密码bcrypt加密
- 请求频率限制
- CORS跨域保护
- SQL注入防护
- XSS攻击防护

## 🔧 故障排除

### 常见问题

**1. 启动脚本报错 "Node.js not found"**
- 请先安装 Node.js：https://nodejs.org/
- 安装后重启命令行窗口

**2. 端口被占用**
- 后端端口3001被占用：修改 `server/.env` 中的 `PORT` 设置
- 前端端口5173被占用：Vite会自动选择其他端口

**3. 数据库初始化失败**
- 删除 `server/database/` 目录下的数据库文件
- 重新运行 `install.bat`

**4. 依赖安装失败**
- 检查网络连接
- 尝试使用淘宝镜像：`npm config set registry https://registry.npmmirror.com`
- 清除npm缓存：`npm cache clean --force`

**5. 前端页面空白**
- 检查浏览器控制台是否有错误
- 确认后端服务器正常运行
- 检查API地址配置

### 启动脚本说明

项目提供了多个启动脚本：

- **`run.bat`** - 简单启动脚本（推荐）
- **`install.bat`** - 依赖安装脚本
- **`restart.bat`** - 完整的重启脚本
- **`start.ps1`** - PowerShell版本启动脚本

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看本文档的常见问题部分
2. 在GitHub上提交Issue
3. 联系项目维护者

---

**班级管理系统** - 让我们共同创造更好的学习环境！ 🎓

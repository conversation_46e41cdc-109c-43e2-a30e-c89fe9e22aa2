@echo off
title Class Management System
echo Starting Class Management System...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js not found. Please install Node.js first.
    pause
    exit /b 1
)

echo Node.js found. Starting servers...
echo.

REM Start backend server
echo Starting backend server...
start "Backend Server" cmd /k "cd /d %~dp0server && npm run dev"

REM Wait 3 seconds
ping 127.0.0.1 -n 4 > nul

REM Start frontend server
echo Starting frontend server...
start "Frontend Server" cmd /k "cd /d %~dp0client && npm run dev"

REM Wait 5 seconds then open browser
ping 127.0.0.1 -n 6 > nul
echo Opening browser...
start http://localhost:5173

echo.
echo System started successfully!
echo Backend: http://localhost:3001
echo Frontend: http://localhost:5173
echo Default admin: admin / admin123
echo.
pause

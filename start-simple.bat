@echo off
echo 班级管理系统 - 简单启动脚本
echo ================================

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 请先安装 Node.js
    pause
    exit
)

REM 进入服务器目录并启动
echo 启动后端服务器...
start "后端" cmd /k "cd /d %~dp0server && npm run dev"

REM 等待3秒
timeout /t 3 >nul

REM 进入客户端目录并启动
echo 启动前端服务器...
start "前端" cmd /k "cd /d %~dp0client && npm run dev"

REM 等待5秒后打开浏览器
timeout /t 5 >nul
echo 打开浏览器...
start http://localhost:5173

echo 启动完成！
echo 默认管理员: admin / admin123
pause

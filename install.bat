@echo off
title Class Management System - Installation
echo ========================================
echo Class Management System - Installation
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js not found!
    echo Please download and install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

REM Install backend dependencies
echo Installing backend dependencies...
cd server
if %errorlevel% neq 0 (
    echo Error: server directory not found!
    pause
    exit /b 1
)

call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install backend dependencies!
    pause
    exit /b 1
)

echo Backend dependencies installed successfully.
echo.

REM Initialize database
echo Initializing database...
call npm run init-db
if %errorlevel% neq 0 (
    echo Warning: Database initialization failed (may already exist)
)
echo.

REM Go back to root directory
cd ..

REM Install frontend dependencies
echo Installing frontend dependencies...
cd client
if %errorlevel% neq 0 (
    echo Error: client directory not found!
    pause
    exit /b 1
)

call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install frontend dependencies!
    pause
    exit /b 1
)

echo Frontend dependencies installed successfully.
echo.

REM Go back to root directory
cd ..

echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo You can now run the system using:
echo   run.bat
echo.
echo Default admin account:
echo   Username: admin
echo   Password: admin123
echo.
pause
